<!-- DataTable Section with Latest DataTables -->

<div class="container-fluid">
    <div class="text-center">
        <h3 id="report_title">Daily fee report</h3>
        <h5>From <span id="fromDate"></span> To <span id="toDate"></span></h5>
    </div>
</div>


<div class="container-fluid">
    <div id="summary_data">
    </div>
     <div id="transaction_data">
    </div>
    <?php echo no_data_message(); ?>
</div>


<script>
function summary_datable(summaryid, transaction_id){
    var summaryTable = $('#'+summaryid).DataTable({
        dom: 'Bfrtip',
        paging: true,
        pageLength: 10,
        pagingType: 'full_numbers',
        searching: false,
        ordering: false,
        info: false,
        language: {
            paginate: {
                first: "&laquo;",
                last: "&raquo;",
                next: "&rsaquo;",
                previous: "&lsaquo;"
            }
        },
        buttons: [
            {
                text: 'Print',
                className: 'btn btn-info',
                action: function (e, dt, node, config) {
                    var summaryHtml = $('#'+summaryid).clone();
                    var transactionHtml = $('#'+transaction_id).clone();
                    summaryHtml.removeClass('dataTable');
                    transactionHtml.removeClass('dataTable');
                    var win = window.open('', '', 'height=800,width=1200');
                    win.document.write('<html><head><title>Print</title>');
                    win.document.write('<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css">');
                    win.document.write('</head><body>');
                    win.document.write('<h2>Accounts Summary</h2>');
                    win.document.write(summaryHtml.prop('outerHTML'));
                    win.document.write('<h2>Transaction Data</h2>');
                    win.document.write(transactionHtml.prop('outerHTML'));
                    win.document.write('<script>window.onload=function(){window.print();window.close();};<\/script>');
                    win.document.write('</body></html>');
                    win.document.close();
                }
            },
            {
                extend: 'excelHtml5',
                text: 'Excel',
                className: 'btn btn-success',
                title: 'Accounts Summary'
            }
        ]
    });

}

function transaction_table(transaction_id){
   var table = $('#'+transaction_id).DataTable({
            scrollX: true,
            scrollY: 200,
            stateSave: true,
            autoWidth: false,
            fixedHeader: true,
            ordering: false,
            paging: true,
            pageLength: 10,
            pagingType: 'full_numbers',
            dom: 'Bfrtip',
            language: {
                paginate: {
                    first: "&laquo;",
                    last: "&raquo;",
                    next: "&rsaquo;",
                    previous: "&lsaquo;"
                }
            },
            buttons: [
                {
                    extend: 'print',
                    className: 'btn btn-info'
                },
                {
                    extend: 'excelHtml5',
                    text: 'Excel',
                    className: 'btn btn-success'
                },
                {
                    extend: 'colvis',
                    className: 'btn btn-warning'
                }
            ]
        });

}

// Enhanced DataTable Functions for Latest Version

// Initialize Summary DataTable with enhanced features
function initializeSummaryDataTable() {
    if ($.fn.DataTable.isDataTable('#summary-table')) {
        $('#summary-table').DataTable().destroy();
    }

    summaryTable = $('#summary-table').DataTable({
        dom: 'Bfrtip',
        paging: true,
        pageLength: 10,
        pagingType: 'full_numbers',
        searching: false,
        ordering: false,
        info: false,
        scrollX: true,
        language: {
            paginate: {
                first: "&laquo;",
                last: "&raquo;",
                next: "&rsaquo;",
                previous: "&lsaquo;"
            }
        },
        buttons: [
            {
                text: '<i class="fa fa-print"></i> Print',
                className: 'btn btn-info btn-sm',
                action: function (e, dt, node, config) {
                    printSummaryAndTransaction();
                }
            },
            {
                extend: 'excelHtml5',
                text: '<i class="fa fa-file-excel-o"></i> Excel',
                className: 'btn btn-success btn-sm',
                title: 'Summary Report',
                filename: function() {
                    var from_date = $('#from_date').val();
                    var to_date = $('#to_date').val();
                    return 'Summary_Report_' + from_date + '_to_' + to_date;
                }
            }
        ]
    });

}

// Initialize Transaction DataTable with enhanced features
function initializeTransactionDataTable() {
    if ($.fn.DataTable.isDataTable('#daily_dataTable')) {
        $('#daily_dataTable').DataTable().destroy();
    }

    detailsTable = $('#daily_dataTable').DataTable({
        dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6 text-right"<"d-inline-block"f><"d-inline-block"B>>>rtip',
        scrollX: true,
        scrollY: 400,
        scrollCollapse: true,
        stateSave: true,
        autoWidth: false,
        fixedHeader: true,
        ordering: true,
        paging: true,
        pageLength: 10,
        pagingType: 'full_numbers',
        language: {
            search: "",
            searchPlaceholder: "Search transactions...",
            lengthMenu: "Show _MENU_ entries",
            info: "Showing _START_ to _END_ of _TOTAL_ entries",
            paginate: {
                first: "&laquo;",
                last: "&raquo;",
                next: "Next &rsaquo;",
                previous:"&lsaquo; Previous"
            },
            emptyTable: "No data available in table",
            zeroRecords: "No matching records found"
        },
        columnDefs: [
            { targets: '_all', className: 'text-center' },
            { targets: [-1, -2, -3, -4], className: 'text-right' }
        ],
        buttons: [
        
            {
                extend: 'colvis',
                text: '<button class="btn btn-outline-primary" id="expbtns"><span class="bi bi-layout-three-columns" aria-hidden="true"></span> Columns</button>',
                // className: 'btn btn-warning btn-sm',
                columns: ':not(.no-export)'
            },
            {
                extend: 'print',
                text: '<button class="btn btn-outline-primary" id="expbtns"><span class="bi bi-printer" aria-hidden="true"></span> Print</button>',
                // className: 'btn btn-info btn-sm',
                exportOptions: {
                    columns: ':visible',
                    // stripHtml: false
                },
            },
            {
                extend: 'excelHtml5',
                text: '<button class="btn btn-outline-primary" id="expbtns"><span class="bi bi-file-earmark-excel" aria-hidden="true"></span> Excel</button>',
                // className: 'btn btn-success btn-sm',
                exportOptions: {
                    columns: ':visible'
                },
                filename: function() {
                    var from_date = $('#from_date').val();
                    var to_date = $('#to_date').val();
                    return 'Transaction_Report_' + from_date + '_to_' + to_date;
                },
                title: function() {
                    var from_date = $('#from_date').val();
                    var to_date = $('#to_date').val();
                    return 'Transaction Report From ' + from_date + ' To ' + to_date;
                }
            }
        ]
    });

    // Handle pagination for footer visibility
    detailsTable.on('page.dt', function() {
        var info = detailsTable.page.info();
        if (info.page + 1 !== info.pages) {
            $('#daily_dataTable tfoot').hide();
        } else {
            $('#daily_dataTable tfoot').show();
        }
    });

}



// Enhanced print function
function printSummaryAndTransaction() {
    var summaryHtml = $('#summary-table').length ? $('#summary-table').clone() : '';
    var transactionHtml = $('#daily_dataTable').length ? $('#daily_dataTable').clone() : '';

    if (summaryHtml) summaryHtml.removeClass('dataTable');
    if (transactionHtml) transactionHtml.removeClass('dataTable');

    var from_date = $('#from_date').val();
    var to_date = $('#to_date').val();
    var report_title = $('#summary_report_title').text() || 'Report';

    var win = window.open('', '', 'height=800,width=1200');
    win.document.write('<html><head><title>' + report_title + '</title>');
    win.document.write('<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">');
    win.document.write('<style>');
    win.document.write('body { font-family: Arial, sans-serif; padding: 20px; }');
    win.document.write('table { width: 100%; border-collapse: collapse; margin: 15px 0; font-size: 12px; }');
    win.document.write('th, td { border: 1px solid #ddd; padding: 6px; text-align: left; }');
    win.document.write('th { background-color: #f2f2f2; font-weight: bold; text-align: center; }');
    win.document.write('.text-right { text-align: right !important; }');
    win.document.write('.text-center { text-align: center !important; }');
    win.document.write('@media print { .no-print { display: none; } }');
    win.document.write('</style>');
    win.document.write('</head><body>');
    win.document.write('<h2 style="text-align: center;">' + report_title + '</h2>');
    win.document.write('<p style="text-align: center;">From ' + from_date + ' To ' + to_date + '</p>');

    if (summaryHtml.length) {
        win.document.write('<h3>Summary</h3>');
        win.document.write(summaryHtml.prop('outerHTML'));
    }

    if (transactionHtml.length) {
        win.document.write('<h3>Transaction Details</h3>');
        win.document.write(transactionHtml.prop('outerHTML'));
    }

    win.document.write('<script>window.onload=function(){window.print();window.close();};<\/script>');
    win.document.write('</body></html>');
    win.document.close();
}

// Export function
function exportToExcel_daily() {
    if (summaryTable && typeof summaryTable.button === 'function') {
        summaryTable.button('.buttons-excel').trigger();
    } else if (detailsTable && typeof detailsTable.button === 'function') {
        detailsTable.button('.buttons-excel').trigger();
    } else {
        console.log('No DataTable instance found for export');
    }
}

// Print function for backward compatibility
function printProfile() {
    printSummaryAndTransaction();
}
</script>

<style>
  /* GENERAL PAGINATION BUTTONS */
  .pagination .page-link {
    text-decoration: none;
    border: none;
    background-color: transparent;
    font-weight: 500;
    color: black;
    border-radius: 5px;
    padding: 0.5rem 0.75rem;
    transition: all 0.2s ease-in-out;
  }

  /* ACTIVE PAGE NUMBER */
  .pagination .page-item.active .page-link {
    background-color: #623CE7;
    color: white;
  }

  /* GENERAL HOVER STATE */
  .pagination .page-link:hover {
    background-color: #462BA4;
    color: white;
  }

  /* FOCUS STATE */
  .pagination .page-link:focus {
    background-color: #623CE7;
    color: white;
    outline: 3px solid #B7A5F4;
    outline-offset: 2px;
  }

  /* PRESSED STATE */
  .pagination .page-link:active {
    background-color: #36217F !important;
    color: white;
  }

  /* DISABLED STATE: NO POINTER, GRAY TEXT, NO BUTTON LOOK */
  .pagination .page-item.disabled .page-link {
    color: #A9A6A9 !important;
    background-color: transparent !important;
    cursor: default !important;
    pointer-events: none;
  }

  /* ALL NAV ARROWS + LABELS: VIOLET BY DEFAULT */
  .pagination .page-link.nav-arrow {
    color: #623CE7 !important;
  }

  /* HOVER FOR ARROWS + LABELS */
  .pagination .page-link.nav-arrow:hover {
    background-color: #462BA4 !important;
    color: white !important;
  }

  /* DataTables specific pagination styling */
  .dataTables_paginate .pagination {
    justify-content: flex-end;
    align-items: center;
  }

  /* Override DataTables default pagination classes */
  .dataTables_paginate .paginate_button {
    text-decoration: none !important;
    border: none !important;
    background-color: transparent !important;
    font-weight: 500 !important;
    color: black !important;
    border-radius: 5px !important;
    padding: 0.5rem 0.75rem !important;
    transition: all 0.2s ease-in-out !important;
    margin: 0 !important;
  }

  .dataTables_paginate .paginate_button:hover {
    background-color: #462BA4 !important;
    color: white !important;
  }

  .dataTables_paginate .paginate_button.current {
    background-color: #623CE7 !important;
    color: white !important;
  }

  .dataTables_paginate .paginate_button.disabled {
    color: #A9A6A9 !important;
    background-color: transparent !important;
    cursor: default !important;
    pointer-events: none !important;
  }

  /* Navigation arrows styling for DataTables */
  .dataTables_paginate .paginate_button.first,
  .dataTables_paginate .paginate_button.previous,
  .dataTables_paginate .paginate_button.next,
  .dataTables_paginate .paginate_button.last {
    color: #623CE7 !important;
  }

  .dataTables_paginate .paginate_button.first:hover,
  .dataTables_paginate .paginate_button.previous:hover,
  .dataTables_paginate .paginate_button.next:hover,
  .dataTables_paginate .paginate_button.last:hover {
    background-color: #462BA4 !important;
    color: white !important;
  }
</style>